/usr/bin/c++ CMakeFiles/testOpencv_vslam2.dir/main.cpp.o CMakeFiles/testOpencv_vslam2.dir/getCalibData.cpp.o CMakeFiles/testOpencv_vslam2.dir/forwardCalib.cpp.o CMakeFiles/testOpencv_vslam2.dir/mapComputer.cpp.o CMakeFiles/testOpencv_vslam2.dir/MSRCR.cpp.o CMakeFiles/testOpencv_vslam2.dir/calibration.cpp.o -o testOpencv_vslam2  -Wl,-rpath,/usr/local/lib /usr/local/lib/libopencv_gapi.so.4.12.0 /usr/local/lib/libopencv_highgui.so.4.12.0 /usr/local/lib/libopencv_ml.so.4.12.0 /usr/local/lib/libopencv_objdetect.so.4.12.0 /usr/local/lib/libopencv_photo.so.4.12.0 /usr/local/lib/libopencv_stitching.so.4.12.0 /usr/local/lib/libopencv_video.so.4.12.0 /usr/local/lib/libopencv_videoio.so.4.12.0 -lyaml-cpp /usr/local/lib/libopencv_imgcodecs.so.4.12.0 /usr/local/lib/libopencv_dnn.so.4.12.0 /usr/local/lib/libopencv_calib3d.so.4.12.0 /usr/local/lib/libopencv_features2d.so.4.12.0 /usr/local/lib/libopencv_flann.so.4.12.0 /usr/local/lib/libopencv_imgproc.so.4.12.0 /usr/local/lib/libopencv_core.so.4.12.0 
