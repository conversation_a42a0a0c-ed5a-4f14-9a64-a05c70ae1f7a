#!/usr/bin/env python3
"""
测试脚本：比较两个项目生成的distance_table文件
"""

import numpy as np
import os

def load_distance_table(table_path):
    """加载distance_table文件"""
    if not os.path.exists(table_path):
        raise FileNotFoundError(f"文件不存在: {table_path}")
    
    with open(table_path, 'rb') as f:
        data = f.read()
    
    # 转换为uint8数组并重塑为(x,y)对
    data = np.frombuffer(data, dtype=np.uint8)
    return data.reshape(-1, 2)

def compare_tables(table1_path, table2_path):
    """比较两个distance_table文件"""
    print(f"比较文件:")
    print(f"  文件1: {table1_path}")
    print(f"  文件2: {table2_path}")
    
    # 加载两个表
    try:
        table1 = load_distance_table(table1_path)
        table2 = load_distance_table(table2_path)
    except FileNotFoundError as e:
        print(f"错误: {e}")
        return
    
    print(f"\n文件大小:")
    print(f"  文件1: {len(table1)} 个坐标对")
    print(f"  文件2: {len(table2)} 个坐标对")
    
    if len(table1) != len(table2):
        print("❌ 文件大小不一致!")
        return
    
    # 比较内容
    differences = np.where((table1 != table2).any(axis=1))[0]
    
    if len(differences) == 0:
        print("✅ 两个文件完全一致!")
    else:
        print(f"❌ 发现 {len(differences)} 个不同的坐标对")
        print(f"差异比例: {len(differences)/len(table1)*100:.2f}%")
        
        # 显示前10个差异
        print("\n前10个差异:")
        for i, diff_idx in enumerate(differences[:10]):
            x1, y1 = table1[diff_idx]
            x2, y2 = table2[diff_idx]
            print(f"  索引 {diff_idx}: ({x1}, {y1}) vs ({x2}, {y2})")
        
        if len(differences) > 10:
            print(f"  ... 还有 {len(differences)-10} 个差异")

def test_specific_pixel(table_path, pixel_x, pixel_y):
    """测试特定像素点的查找结果"""
    # 参数设置（与Python检测器一致）
    IMGX_RANGE = [30, 1250]
    IMGY_RANGE = [400, 720]
    X_MIN, X_MAX = 0, 82
    Y_MIN, Y_MAX = -125, 125
    
    TABLE_WIDTH = IMGX_RANGE[1] - IMGX_RANGE[0] + 1
    
    print(f"\n测试像素点 ({pixel_x}, {pixel_y}):")
    
    # 检查像素点是否在有效范围内
    if not (IMGX_RANGE[0] <= pixel_x <= IMGX_RANGE[1] and 
            IMGY_RANGE[0] <= pixel_y <= IMGY_RANGE[1]):
        print(f"❌ 像素点超出有效范围!")
        print(f"   有效范围: X{IMGX_RANGE}, Y{IMGY_RANGE}")
        return
    
    try:
        table = load_distance_table(table_path)
        
        # 计算索引
        row_offset = pixel_y - IMGY_RANGE[0]
        col_offset = pixel_x - IMGX_RANGE[0]
        index = row_offset * TABLE_WIDTH + col_offset
        
        print(f"  计算索引: {index}")
        print(f"  行偏移: {row_offset}, 列偏移: {col_offset}")
        
        if index >= len(table):
            print(f"❌ 索引超出范围! 最大索引: {len(table)-1}")
            return
        
        x_value, y_value = table[index]
        print(f"  原始值: ({x_value}, {y_value})")
        
        # 转换为物理坐标
        physical_x = X_MIN + (x_value / 255.0) * (X_MAX - X_MIN)
        physical_y = Y_MIN + (y_value / 255.0) * (Y_MAX - Y_MIN)
        
        print(f"  物理坐标: ({physical_x:.3f}, {physical_y:.3f})")
        
        if x_value == 0 and y_value == 0:
            print("  ⚠️  检测到 (0,0) 值，可能表示无效区域")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def main():
    print("Distance Table 比较工具")
    print("=" * 50)
    
    # 定义文件路径
    table1_path = "CALIB_t11_ceju/build/distance_table"
    table2_path = "CALIB_t12_ceju/build/output/distance_table"
    
    # 比较两个表
    compare_tables(table1_path, table2_path)
    
    # 测试特定像素点（来自检测框）
    test_pixels = [
        (545, 402),  # 左上角
        (653, 402),  # 右上角
        (545, 444),  # 左下角
        (653, 444),  # 右下角
    ]
    
    print("\n" + "=" * 50)
    print("测试检测框的关键像素点:")
    
    for table_name, table_path in [("CALIB_t11_ceju", table1_path), 
                                   ("CALIB_t12_ceju", table2_path)]:
        print(f"\n使用 {table_name} 的 distance_table:")
        for pixel_x, pixel_y in test_pixels:
            test_specific_pixel(table_path, pixel_x, pixel_y)

if __name__ == "__main__":
    main()
