#!/usr/bin/env python3
"""
鲁棒的距离计算器 - 专门处理无效标定区域的物理距离计算
"""

import numpy as np
from dataclasses import dataclass
from typing import Tuple, Optional
from src.pixel_converter import PixelConverter

@dataclass
class BBox:
    x1: int
    y1: int
    x2: int
    y2: int

@dataclass
class DistanceResult:
    """距离计算结果"""
    left_bottom_distance: float = 0.0    # 左下角距离相机的物理距离
    right_bottom_distance: float = 0.0   # 右下角距离相机的物理距离
    average_distance: float = 0.0        # 平均距离
    width: float = 0.0                   # 目标宽度
    height: float = 0.0                  # 目标高度
    confidence: float = 0.0              # 计算置信度 (0-1)
    method_used: str = ""                # 使用的计算方法
    is_valid: bool = False               # 结果是否有效

class RobustDistanceCalculator:
    """鲁棒的距离计算器"""
    
    def __init__(self, distance_table_path: str):
        self.converter = PixelConverter(distance_table_path)
        
    def is_valid_coordinate(self, x: float, y: float) -> bool:
        """检查物理坐标是否有效"""
        return not (abs(x) < 0.01 and abs(y - 0.49) < 0.1)
    
    def find_nearest_valid_point(self, pixel_x: int, pixel_y: int, max_radius: int = 50) -> Tuple[Optional[float], Optional[float], int]:
        """搜索最近的有效物理坐标点，返回(x, y, search_radius)"""
        for radius in range(1, max_radius + 1):
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    if abs(dx) != radius and abs(dy) != radius:
                        continue
                        
                    search_x = pixel_x + dx
                    search_y = pixel_y + dy
                    
                    if self.converter.is_valid_pixel_coordinate(search_x, search_y):
                        try:
                            phys_x, phys_y = self.converter.query_physical_location(search_x, search_y)
                            if self.is_valid_coordinate(phys_x, phys_y):
                                return phys_x, phys_y, radius
                        except:
                            continue
        return None, None, max_radius
    
    def estimate_depth_from_y(self, pixel_y: int) -> float:
        """基于Y坐标估算深度"""
        IMGY_RANGE = [400, 720]
        DEPTH_RANGE = [10, 80]
        
        if pixel_y < IMGY_RANGE[0]:
            return DEPTH_RANGE[1]
        elif pixel_y > IMGY_RANGE[1]:
            return DEPTH_RANGE[0]
        else:
            y_ratio = (pixel_y - IMGY_RANGE[0]) / (IMGY_RANGE[1] - IMGY_RANGE[0])
            return DEPTH_RANGE[1] - y_ratio * (DEPTH_RANGE[1] - DEPTH_RANGE[0])
    
    def get_robust_physical_coordinate(self, pixel_x: int, pixel_y: int) -> Tuple[Optional[float], Optional[float], str, float]:
        """获取鲁棒的物理坐标，返回(x, y, method, confidence)"""
        try:
            # 尝试直接查询
            phys_x, phys_y = self.converter.query_physical_location(pixel_x, pixel_y)
            
            if self.is_valid_coordinate(phys_x, phys_y):
                return phys_x, phys_y, "direct", 1.0
            
            # 搜索最近的有效点
            valid_x, valid_y, radius = self.find_nearest_valid_point(pixel_x, pixel_y)
            if valid_x is not None:
                confidence = max(0.1, 1.0 - radius / 50.0)  # 距离越近置信度越高
                return valid_x, valid_y, f"nearest_neighbor(r={radius})", confidence
            
            # 使用经验估算
            estimated_depth = self.estimate_depth_from_y(pixel_y)
            return estimated_depth, 0.0, "depth_estimation", 0.3
            
        except Exception as e:
            return None, None, f"failed: {e}", 0.0
    
    def calculate_distance(self, bbox: BBox, verbose: bool = True) -> DistanceResult:
        """计算检测框的物理距离"""
        result = DistanceResult()
        
        if verbose:
            print(f"🎯 计算检测框距离: ({bbox.x1}, {bbox.y1}) -> ({bbox.x2}, {bbox.y2})")
        
        # 获取关键点的物理坐标
        left_bottom_x, left_bottom_y, lb_method, lb_conf = self.get_robust_physical_coordinate(bbox.x1, bbox.y2)
        right_bottom_x, right_bottom_y, rb_method, rb_conf = self.get_robust_physical_coordinate(bbox.x2, bbox.y2)
        
        if verbose:
            print(f"📍 左下角 ({bbox.x1}, {bbox.y2}): 物理坐标({left_bottom_x:.3f}, {left_bottom_y:.3f}) - {lb_method}")
            print(f"📍 右下角 ({bbox.x2}, {bbox.y2}): 物理坐标({right_bottom_x:.3f}, {right_bottom_y:.3f}) - {rb_method}")
        
        # 检查是否获取到有效坐标
        if left_bottom_x is None or right_bottom_x is None:
            if verbose:
                print("❌ 无法获取有效的底部坐标")
            return result
        
        # 计算距离
        result.left_bottom_distance = left_bottom_x
        result.right_bottom_distance = right_bottom_x
        result.average_distance = (left_bottom_x + right_bottom_x) / 2
        
        # 计算宽度
        result.width = np.sqrt((right_bottom_x - left_bottom_x)**2 + (right_bottom_y - left_bottom_y)**2)
        
        # 尝试计算高度
        left_top_x, left_top_y, lt_method, lt_conf = self.get_robust_physical_coordinate(bbox.x1, bbox.y1)
        if left_top_x is not None:
            result.height = np.sqrt((left_top_x - left_bottom_x)**2 + (left_top_y - left_bottom_y)**2)
            if verbose:
                print(f"📍 左上角 ({bbox.x1}, {bbox.y1}): 物理坐标({left_top_x:.3f}, {left_top_y:.3f}) - {lt_method}")
        else:
            # 使用像素高度估算
            pixel_height = bbox.y2 - bbox.y1
            result.height = pixel_height * 0.15  # 经验系数
            if verbose:
                print(f"📏 使用像素高度估算: {pixel_height} px -> {result.height:.3f} cm")
        
        # 计算置信度
        result.confidence = min(lb_conf, rb_conf)
        
        # 设置使用的方法
        result.method_used = f"LB:{lb_method}, RB:{rb_method}"
        
        # 判断结果是否有效
        result.is_valid = (result.left_bottom_distance > 0 and 
                          result.right_bottom_distance > 0 and
                          result.confidence > 0.1)
        
        if verbose:
            print(f"📊 计算结果:")
            print(f"   左下角距离: {result.left_bottom_distance:.2f} cm")
            print(f"   右下角距离: {result.right_bottom_distance:.2f} cm")
            print(f"   平均距离: {result.average_distance:.2f} cm")
            print(f"   目标宽度: {result.width:.2f} cm")
            print(f"   目标高度: {result.height:.2f} cm")
            print(f"   计算置信度: {result.confidence:.2f}")
            print(f"   方法: {result.method_used}")
            print(f"   结果有效: {'✅' if result.is_valid else '❌'}")
        
        return result

def main():
    """测试示例"""
    print("🚀 鲁棒距离计算器测试")
    print("=" * 60)
    
    # 创建计算器
    calculator = RobustDistanceCalculator("config/distance_table")
    
    # 测试用例
    test_cases = [
        {
            "name": "部分无效区域检测框",
            "bbox": BBox(x1=545, y1=414, x2=653, y2=444),
            "description": "左上角在无效区域，底部在有效区域"
        },
        {
            "name": "完全有效区域检测框", 
            "bbox": BBox(x1=545, y1=444, x2=653, y2=474),
            "description": "所有角点都在有效区域"
        },
        {
            "name": "中心区域检测框",
            "bbox": BBox(x1=600, y1=500, x2=700, y2=550),
            "description": "图像中心区域"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"📝 {test_case['description']}")
        print("-" * 60)
        
        result = calculator.calculate_distance(test_case['bbox'])
        
        if result.is_valid:
            print(f"🎉 成功计算出物理距离！")
            print(f"💡 建议使用平均距离: {result.average_distance:.2f} cm")
        else:
            print(f"⚠️  计算结果可能不准确，请谨慎使用")
        
        print("=" * 60)

if __name__ == "__main__":
    main()
