import yaml
import numpy as np
from dataclasses import dataclass
from typing import Tuple
from src.pixel_converter import PixelConverter

@dataclass
class BBox:
    x1: int
    y1: int
    x2: int
    y2: int

@dataclass
class DetectionResult:
    bbox: BBox
    label: str
    confidence: float
    physical_distance: float = 0.0
    left_distance: float = 0.0
    right_distance: float = 0.0

class DetectionProcessor:
    def __init__(self):
        self.size_ranges_config = None

    def set_size_ranges_config(self, config):
        """Set the size ranges configuration from YAML."""
        self.size_ranges_config = config

    def is_size_reasonable_for_label(self, label: str, max_size: float, min_size: float) -> bool:
        """Check if the object size is reasonable for its label."""
        try:
            object_config = self.size_ranges_config["objects"].get(label, self.size_ranges_config["default"])
            
            config_max_size = object_config["max_size"]
            config_min_size = object_config["min_size"]
            description = object_config["description"]

            if max_size > config_max_size or min_size < config_min_size:
                print(f"警告：{description}")
                return False
            return True
        except Exception as e:
            print(f"Error checking size ranges: {str(e)}")
            return False

    def is_valid_coordinate(self, x: float, y: float) -> bool:
        """检查物理坐标是否有效（不是无效区域标记）"""
        # 检查是否为无效区域标记 (0.000, 0.490) 或类似的值
        return not (abs(x) < 0.01 and abs(y - 0.49) < 0.1)

    def find_nearest_valid_point(self, converter: PixelConverter, pixel_x: int, pixel_y: int, max_search_radius: int = 50) -> Tuple[float, float]:
        """在给定像素点周围搜索最近的有效物理坐标点"""
        print(f"🔍 搜索像素点 ({pixel_x}, {pixel_y}) 周围的有效坐标...")

        for radius in range(1, max_search_radius + 1):
            # 搜索以当前点为中心的正方形区域
            for dx in range(-radius, radius + 1):
                for dy in range(-radius, radius + 1):
                    # 只检查边界点，避免重复搜索内部点
                    if abs(dx) != radius and abs(dy) != radius:
                        continue

                    search_x = pixel_x + dx
                    search_y = pixel_y + dy

                    # 检查搜索点是否在有效范围内
                    if converter.is_valid_pixel_coordinate(search_x, search_y):
                        try:
                            phys_x, phys_y = converter.query_physical_location(search_x, search_y)
                            if self.is_valid_coordinate(phys_x, phys_y):
                                print(f"✅ 找到有效点: 像素({search_x}, {search_y}) -> 物理({phys_x:.3f}, {phys_y:.3f}), 搜索半径: {radius}")
                                return phys_x, phys_y
                        except:
                            continue

        print(f"❌ 在半径 {max_search_radius} 内未找到有效坐标点")
        return None, None

    def interpolate_from_valid_neighbors(self, converter: PixelConverter, pixel_x: int, pixel_y: int) -> Tuple[float, float]:
        """通过周围有效点进行插值估算"""
        print(f"🧮 尝试通过插值估算像素点 ({pixel_x}, {pixel_y}) 的物理坐标...")

        valid_points = []
        search_offsets = [(-10, 0), (10, 0), (0, -10), (0, 10), (-20, 0), (20, 0), (0, -20), (0, 20)]

        for dx, dy in search_offsets:
            search_x = pixel_x + dx
            search_y = pixel_y + dy

            if converter.is_valid_pixel_coordinate(search_x, search_y):
                try:
                    phys_x, phys_y = converter.query_physical_location(search_x, search_y)
                    if self.is_valid_coordinate(phys_x, phys_y):
                        valid_points.append((search_x, search_y, phys_x, phys_y))
                        print(f"  有效参考点: 像素({search_x}, {search_y}) -> 物理({phys_x:.3f}, {phys_y:.3f})")
                except:
                    continue

        if len(valid_points) >= 2:
            # 使用最近的两个点进行线性插值
            valid_points.sort(key=lambda p: (p[0] - pixel_x)**2 + (p[1] - pixel_y)**2)
            p1, p2 = valid_points[0], valid_points[1]

            # 计算插值权重
            dist1 = np.sqrt((p1[0] - pixel_x)**2 + (p1[1] - pixel_y)**2)
            dist2 = np.sqrt((p2[0] - pixel_x)**2 + (p2[1] - pixel_y)**2)

            if dist1 + dist2 > 0:
                w1 = dist2 / (dist1 + dist2)  # 距离越近权重越大
                w2 = dist1 / (dist1 + dist2)

                interp_x = w1 * p1[2] + w2 * p2[2]
                interp_y = w1 * p1[3] + w2 * p2[3]

                print(f"✅ 插值结果: ({interp_x:.3f}, {interp_y:.3f})")
                return interp_x, interp_y

        print(f"❌ 插值失败，有效参考点不足")
        return None, None

    def get_robust_physical_coordinate(self, converter: PixelConverter, pixel_x: int, pixel_y: int) -> Tuple[float, float]:
        """获取鲁棒的物理坐标，处理无效区域"""
        try:
            # 首先尝试直接查询
            phys_x, phys_y = converter.query_physical_location(pixel_x, pixel_y)

            if self.is_valid_coordinate(phys_x, phys_y):
                return phys_x, phys_y

            print(f"⚠️  像素点 ({pixel_x}, {pixel_y}) 在无效标定区域，物理坐标: ({phys_x:.3f}, {phys_y:.3f})")

            # 策略1: 搜索最近的有效点
            valid_x, valid_y = self.find_nearest_valid_point(converter, pixel_x, pixel_y)
            if valid_x is not None:
                return valid_x, valid_y

            # 策略2: 通过插值估算
            interp_x, interp_y = self.interpolate_from_valid_neighbors(converter, pixel_x, pixel_y)
            if interp_x is not None:
                return interp_x, interp_y

            # 策略3: 使用经验估算（基于Y坐标的深度估算）
            print(f"🔧 使用经验估算方法...")
            estimated_depth = self.estimate_depth_from_y_coordinate(pixel_y)
            print(f"✅ 经验估算深度: {estimated_depth:.3f} cm")
            return estimated_depth, 0.0  # Y坐标设为0（中心线）

        except Exception as e:
            print(f"❌ 获取物理坐标失败: {e}")
            return None, None

    def estimate_depth_from_y_coordinate(self, pixel_y: int) -> float:
        """基于Y像素坐标估算深度（经验方法）"""
        # 基于相机标定的经验公式：Y坐标越大，距离相机越近
        # 这里使用简单的线性映射，实际应用中可以根据具体标定数据调整

        # 参数设置
        IMGY_RANGE = [400, 720]
        DEPTH_RANGE = [10, 80]  # 对应的深度范围（cm）

        # 将Y坐标映射到深度
        if pixel_y < IMGY_RANGE[0]:
            return DEPTH_RANGE[1]  # 图像顶部对应最远距离
        elif pixel_y > IMGY_RANGE[1]:
            return DEPTH_RANGE[0]  # 图像底部对应最近距离
        else:
            # 线性插值
            y_ratio = (pixel_y - IMGY_RANGE[0]) / (IMGY_RANGE[1] - IMGY_RANGE[0])
            depth = DEPTH_RANGE[1] - y_ratio * (DEPTH_RANGE[1] - DEPTH_RANGE[0])
            return depth

    def calculate_target_size(self, bbox: BBox, converter: PixelConverter) -> Tuple[float, float, float, float]:
        """Calculate the target size and return size_y, size_x, z1, z2."""
        try:
            print(f"📐 计算目标尺寸，检测框: ({bbox.x1}, {bbox.y1}) -> ({bbox.x2}, {bbox.y2})")

            # 使用鲁棒方法获取物理坐标
            x1, y1 = self.get_robust_physical_coordinate(converter, bbox.x1, bbox.y2)  # bottom left
            x2, y2 = self.get_robust_physical_coordinate(converter, bbox.x2, bbox.y2)  # bottom right
            x3, y3 = self.get_robust_physical_coordinate(converter, bbox.x1, bbox.y1)  # top left

            # 检查是否所有坐标都有效
            if any(coord is None for coord in [x1, y1, x2, y2, x3, y3]):
                print("❌ 无法获取有效的物理坐标")
                return 0.0, 0.0, 0.0, 0.0

            # Print physical coordinates
            print(f"📍 像素坐标 ({bbox.x1}, {bbox.y2}) -> 物理坐标 ({x1:.3f}, {y1:.3f}) [左下]")
            print(f"📍 像素坐标 ({bbox.x2}, {bbox.y2}) -> 物理坐标 ({x2:.3f}, {y2:.3f}) [右下]")
            print(f"📍 像素坐标 ({bbox.x1}, {bbox.y1}) -> 物理坐标 ({x3:.3f}, {y3:.3f}) [左上]")

            # Calculate lengths
            bottom_length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)  # 底边长度（横向）
            left_length = np.sqrt((x3 - x1)**2 + (y3 - y1)**2)    # 左边长度（纵向）

            # 如果左边长度为0，尝试使用像素距离估算
            if left_length < 0.1:  # 几乎为0
                print("⚠️  左边长度几乎为0，使用像素距离估算...")
                pixel_height = bbox.y2 - bbox.y1  # 像素高度
                # 基于经验：每个像素在深度方向大约对应0.1-0.2cm（根据实际标定调整）
                estimated_height = pixel_height * 0.15  # 经验系数
                left_length = estimated_height
                print(f"📏 像素高度: {pixel_height} px -> 估算高度: {estimated_height:.3f} cm")

            print("----------------------------------------")
            print(f"📏 底边长度（横向）: {bottom_length:.3f} cm")
            print(f"📏 左边长度（纵向）: {left_length:.3f} cm")
            print(f"📏 左下角距离相机: {x1:.3f} cm")
            print(f"📏 右下角距离相机: {x2:.3f} cm")
            print("----------------------------------------")

            return bottom_length, left_length, x1, x2

        except Exception as e:
            print(f"❌ Error calculating target size: {str(e)}")
            return 0.0, 0.0, 0.0, 0.0

    def is_detection_reasonable(self, det: DetectionResult, table_path: str) -> Tuple[bool, float, float]:
        """Check if the detection result is reasonable."""
        if det is None:
            print("检测结果为空!")
            return False, 0.0, 0.0

        converter = PixelConverter(table_path)

        # Check if box is in image range
        if not converter.is_valid_pixel_coordinate(det.bbox.x1, det.bbox.y1) or \
           not converter.is_valid_pixel_coordinate(det.bbox.x2, det.bbox.y2):
            print("警告：检测框超出图像范围")
            return False, 0.0, 0.0

        # Check if box is too small
        box_area = (det.bbox.x2 - det.bbox.x1) * (det.bbox.y2 - det.bbox.y1)
        if box_area < 10:
            print(f"警告：检测框面积过小 ({box_area} 像素)")
            return False, 0.0, 0.0

        # Calculate target size
        size_y, size_x, z1, z2 = self.calculate_target_size(det.bbox, converter)
        if size_y == 0.0 and size_x == 0.0:
            print("无法计算目标尺寸")
            return False, 0.0, 0.0

        # Check if distance is reasonable
        distance = abs(size_x)
        if distance > PixelConverter.X_MAX:
            print(f"警告：目标距离过远 ({distance:.1f} cm)")
            return False, 0.0, 0.0

        # Print target size
        print(f"\n目标尺寸:")
        print(f"长度: {size_y:.3f} cm")
        print(f"宽度: {size_x:.3f} cm")

        # Check size against label
        max_size = max(size_x, size_y)
        min_size = min(size_x, size_y)
        if not self.is_size_reasonable_for_label(det.label, max_size, min_size):
            return False, 0.0, 0.0

        return True, z1, z2

    def process_detection_result(self, det: DetectionResult, table_path: str):
        """Process the detection result and update physical distances."""
        print("\n处理检测结果:")
        print(f"标签: {det.label}, 置信度: {det.confidence}")
        print("----------------------------------------")

        is_reasonable, z1, z2 = self.is_detection_reasonable(det, table_path)
        if not is_reasonable:
            print("检测结果不合理，跳过处理")
            return

        # Update detection result with physical distances
        det.left_distance = z1
        det.right_distance = z2
        det.physical_distance = (z1 + z2) / 2  # Use average of left and right distances 