#!/usr/bin/env python3
"""
测试无效标定区域的处理策略
"""

import yaml
from src.detection_processor import DetectionProcessor, DetectionResult, BBox

def test_invalid_region_cases():
    """测试各种无效区域的情况"""
    
    # 加载配置
    with open("config/size_ranges.yaml", 'r') as f:
        config = yaml.safe_load(f)
    
    processor = DetectionProcessor()
    processor.set_size_ranges_config(config)
    
    # 测试用例：不同的检测框位置
    test_cases = [
        {
            "name": "原始测试用例（部分无效区域）",
            "bbox": BBox(x1=545, y1=414, x2=653, y2=444),
            "description": "左上角在无效区域，左下和右下在有效区域"
        },
        {
            "name": "完全在有效区域",
            "bbox": BBox(x1=545, y1=444, x2=653, y2=474),
            "description": "所有角点都在有效标定区域内"
        },
        {
            "name": "更高的检测框（跨越更多区域）",
            "bbox": BBox(x1=545, y1=400, x2=653, y2=444),
            "description": "从图像顶部到有效区域"
        },
        {
            "name": "中心区域检测框",
            "bbox": BBox(x1=600, y1=500, x2=700, y2=550),
            "description": "完全在图像中心的有效区域"
        },
        {
            "name": "边缘区域检测框",
            "bbox": BBox(x1=100, y1=410, x2=200, y2=440),
            "description": "在图像左边缘的区域"
        }
    ]
    
    print("🧪 无效标定区域处理策略测试")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"📝 描述: {test_case['description']}")
        print(f"📦 检测框: ({test_case['bbox'].x1}, {test_case['bbox'].y1}) -> ({test_case['bbox'].x2}, {test_case['bbox'].y2})")
        print("-" * 60)
        
        # 创建检测结果
        det = DetectionResult(
            bbox=test_case['bbox'],
            label="bin",
            confidence=0.95
        )
        
        # 处理检测结果
        try:
            processor.process_detection_result(det, "config/distance_table")
            
            print(f"✅ 处理结果:")
            print(f"   最终物理距离: {det.physical_distance:.2f} cm")
            print(f"   左下角距离: {det.left_distance:.2f} cm")
            print(f"   右下角距离: {det.right_distance:.2f} cm")
            
            if det.physical_distance > 0:
                print(f"   🎯 成功计算出物理距离！")
            else:
                print(f"   ⚠️  未能计算出有效的物理距离")
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
        
        print("=" * 80)

def test_strategy_comparison():
    """比较不同策略的效果"""
    print("\n🔬 策略效果对比分析")
    print("=" * 80)
    
    # 问题检测框
    problem_bbox = BBox(x1=545, y1=414, x2=653, y2=444)
    
    print(f"📦 问题检测框: ({problem_bbox.x1}, {problem_bbox.y1}) -> ({problem_bbox.x2}, {problem_bbox.y2})")
    print("\n🎯 分析各个角点的处理策略:")
    
    from src.pixel_converter import PixelConverter
    converter = PixelConverter("config/distance_table")
    processor = DetectionProcessor()
    
    # 测试各个角点
    corners = [
        ("左上角", problem_bbox.x1, problem_bbox.y1),
        ("右上角", problem_bbox.x2, problem_bbox.y1),
        ("左下角", problem_bbox.x1, problem_bbox.y2),
        ("右下角", problem_bbox.x2, problem_bbox.y2)
    ]
    
    for corner_name, x, y in corners:
        print(f"\n📍 {corner_name} ({x}, {y}):")
        
        # 直接查询
        try:
            direct_x, direct_y = converter.query_physical_location(x, y)
            is_valid = processor.is_valid_coordinate(direct_x, direct_y)
            print(f"   直接查询: ({direct_x:.3f}, {direct_y:.3f}) - {'✅有效' if is_valid else '❌无效'}")
            
            if not is_valid:
                # 使用鲁棒方法
                robust_x, robust_y = processor.get_robust_physical_coordinate(converter, x, y)
                if robust_x is not None:
                    print(f"   鲁棒方法: ({robust_x:.3f}, {robust_y:.3f}) - ✅成功修复")
                else:
                    print(f"   鲁棒方法: 失败 - ❌无法修复")
        except Exception as e:
            print(f"   查询失败: {e}")

def main():
    print("🚀 无效标定区域处理测试工具")
    print("=" * 80)
    
    # 测试各种无效区域情况
    test_invalid_region_cases()
    
    # 策略对比分析
    test_strategy_comparison()
    
    print("\n📋 总结建议:")
    print("1. ✅ 最近邻搜索: 适用于局部无效区域，效果最好")
    print("2. 🧮 插值估算: 适用于稀疏无效区域，需要足够的有效参考点")
    print("3. 🔧 经验估算: 适用于大面积无效区域，基于Y坐标的深度估算")
    print("4. 📏 像素距离估算: 适用于高度计算，当纵向物理距离无法计算时")
    print("\n💡 建议优先级: 最近邻搜索 > 插值估算 > 经验估算")

if __name__ == "__main__":
    main()
