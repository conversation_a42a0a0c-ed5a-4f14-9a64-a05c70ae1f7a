#!/usr/bin/env python3
"""
使用示例：如何处理无效标定区域的目标检测距离计算
"""

from robust_distance_calculator import RobustDistanceCalculator, BBox

def main():
    print("🎯 无效标定区域处理 - 实际使用示例")
    print("=" * 70)
    
    # 创建鲁棒距离计算器
    calculator = RobustDistanceCalculator("config/distance_table")
    
    # 您的实际检测框（左上角在无效区域）
    detection_bbox = BBox(x1=545, y1=414, x2=653, y2=444)
    
    print("📦 目标检测结果:")
    print(f"   检测框: ({detection_bbox.x1}, {detection_bbox.y1}) -> ({detection_bbox.x2}, {detection_bbox.y2})")
    print(f"   问题: 左上角 ({detection_bbox.x1}, {detection_bbox.y1}) 在无效标定区域")
    print()
    
    # 计算物理距离
    result = calculator.calculate_distance(detection_bbox)
    
    print("\n" + "=" * 70)
    print("🎉 解决方案总结:")
    
    if result.is_valid:
        print(f"✅ 成功获取物理距离信息!")
        print(f"📏 左下角距离相机: {result.left_bottom_distance:.2f} cm")
        print(f"📏 右下角距离相机: {result.right_bottom_distance:.2f} cm") 
        print(f"📏 平均距离: {result.average_distance:.2f} cm")
        print(f"📏 目标宽度: {result.width:.2f} cm")
        print(f"📏 目标高度: {result.height:.2f} cm")
        print(f"🎯 计算置信度: {result.confidence:.2f}")
        
        print(f"\n💡 推荐使用:")
        print(f"   - 主要距离参考: {result.average_distance:.2f} cm")
        print(f"   - 左下角距离: {result.left_bottom_distance:.2f} cm")
        print(f"   - 右下角距离: {result.right_bottom_distance:.2f} cm")
        
    else:
        print(f"❌ 无法获取可靠的物理距离")
        print(f"   置信度过低: {result.confidence:.2f}")
        print(f"   建议检查检测框位置或使用其他方法")
    
    print(f"\n🔧 使用的处理策略: {result.method_used}")
    
    print("\n" + "=" * 70)
    print("📚 处理策略说明:")
    print("1. 🎯 直接查询: 对于有效标定区域的像素点")
    print("2. 🔍 最近邻搜索: 对于无效区域，搜索最近的有效点")
    print("3. 🧮 插值估算: 基于周围有效点进行插值")
    print("4. 📏 经验估算: 基于Y坐标的深度估算和像素距离估算")
    print("\n💡 优先级: 直接查询 > 最近邻搜索 > 插值估算 > 经验估算")

if __name__ == "__main__":
    main()
