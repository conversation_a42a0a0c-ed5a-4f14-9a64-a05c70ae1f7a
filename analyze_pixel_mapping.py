#!/usr/bin/env python3
"""
分析像素坐标映射的详细过程
"""

import numpy as np
import os

def load_distance_table(table_path):
    """加载distance_table文件"""
    with open(table_path, 'rb') as f:
        data = f.read()
    data = np.frombuffer(data, dtype=np.uint8)
    return data.reshape(-1, 2)

def analyze_pixel_mapping(table_path, pixel_x, pixel_y):
    """详细分析像素点的映射过程"""
    # 参数设置（与Python检测器一致）
    IMGX_RANGE = [30, 1250]
    IMGY_RANGE = [400, 720]
    X_MIN, X_MAX = 0, 82
    Y_MIN, Y_MAX = -125, 125
    
    TABLE_WIDTH = IMGX_RANGE[1] - IMGX_RANGE[0] + 1
    
    print(f"🔍 分析像素点 ({pixel_x}, {pixel_y}) 的映射过程")
    print("=" * 60)
    
    # 1. 检查像素点是否在有效范围内
    print(f"1. 范围检查:")
    print(f"   有效X范围: {IMGX_RANGE}")
    print(f"   有效Y范围: {IMGY_RANGE}")
    print(f"   像素点: ({pixel_x}, {pixel_y})")
    
    if not (IMGX_RANGE[0] <= pixel_x <= IMGX_RANGE[1] and 
            IMGY_RANGE[0] <= pixel_y <= IMGY_RANGE[1]):
        print(f"   ❌ 像素点超出有效范围!")
        return
    else:
        print(f"   ✅ 像素点在有效范围内")
    
    # 2. 计算索引
    row_offset = pixel_y - IMGY_RANGE[0]
    col_offset = pixel_x - IMGX_RANGE[0]
    index = row_offset * TABLE_WIDTH + col_offset
    
    print(f"\n2. 索引计算:")
    print(f"   行偏移: {pixel_y} - {IMGY_RANGE[0]} = {row_offset}")
    print(f"   列偏移: {pixel_x} - {IMGX_RANGE[0]} = {col_offset}")
    print(f"   表格宽度: {TABLE_WIDTH}")
    print(f"   计算索引: {row_offset} × {TABLE_WIDTH} + {col_offset} = {index}")
    
    # 3. 加载表格数据
    try:
        table = load_distance_table(table_path)
        print(f"   表格总大小: {len(table)} 个坐标对")
        
        if index >= len(table):
            print(f"   ❌ 索引超出范围! 最大索引: {len(table)-1}")
            return
        
        x_value, y_value = table[index]
        print(f"   ✅ 索引有效，原始值: ({x_value}, {y_value})")
        
    except Exception as e:
        print(f"   ❌ 加载表格失败: {e}")
        return
    
    # 4. 分析原始值的含义
    print(f"\n3. 原始值分析:")
    print(f"   X原始值: {x_value} (0-255范围)")
    print(f"   Y原始值: {y_value} (0-255范围)")
    
    if x_value == 0 and y_value == 0:
        print(f"   ⚠️  检测到 (0,0) 值 - 这表示无效区域或未标定区域")
    elif x_value == 0:
        print(f"   ⚠️  X值为0 - 可能表示该像素点在标定范围边界或无效区域")
    
    # 5. 转换为物理坐标
    physical_x = X_MIN + (x_value / 255.0) * (X_MAX - X_MIN)
    physical_y = Y_MIN + (y_value / 255.0) * (Y_MAX - Y_MIN)
    
    print(f"\n4. 物理坐标转换:")
    print(f"   X转换: {X_MIN} + ({x_value}/255) × ({X_MAX}-{X_MIN}) = {physical_x:.3f}")
    print(f"   Y转换: {Y_MIN} + ({y_value}/255) × ({Y_MAX}-{Y_MIN}) = {physical_y:.3f}")
    print(f"   最终物理坐标: ({physical_x:.3f}, {physical_y:.3f})")
    
    # 6. 分析周围像素点
    print(f"\n5. 周围像素点分析:")
    neighbors = [
        (pixel_x-1, pixel_y-1), (pixel_x, pixel_y-1), (pixel_x+1, pixel_y-1),
        (pixel_x-1, pixel_y),   (pixel_x, pixel_y),   (pixel_x+1, pixel_y),
        (pixel_x-1, pixel_y+1), (pixel_x, pixel_y+1), (pixel_x+1, pixel_y+1)
    ]
    
    for i, (nx, ny) in enumerate(neighbors):
        if (IMGX_RANGE[0] <= nx <= IMGX_RANGE[1] and 
            IMGY_RANGE[0] <= ny <= IMGY_RANGE[1]):
            
            n_row_offset = ny - IMGY_RANGE[0]
            n_col_offset = nx - IMGX_RANGE[0]
            n_index = n_row_offset * TABLE_WIDTH + n_col_offset
            
            if n_index < len(table):
                n_x_val, n_y_val = table[n_index]
                n_phys_x = X_MIN + (n_x_val / 255.0) * (X_MAX - X_MIN)
                n_phys_y = Y_MIN + (n_y_val / 255.0) * (Y_MAX - Y_MIN)
                
                marker = "🎯" if (nx, ny) == (pixel_x, pixel_y) else "  "
                print(f"   {marker}({nx:4d}, {ny:3d}): 原始({n_x_val:3d}, {n_y_val:3d}) -> 物理({n_phys_x:6.2f}, {n_phys_y:6.2f})")

def analyze_mapping_logic():
    """分析映射逻辑的原理"""
    print(f"\n" + "=" * 60)
    print("📚 Distance Table 映射逻辑说明")
    print("=" * 60)
    
    print("""
🔍 为什么会出现 (0.000, 0.490) 这样的坐标？

1. **Distance Table 的生成过程**:
   - 对于图像范围内的每个像素点，系统尝试通过三角形插值计算其物理坐标
   - 如果像素点不在任何标定的三角形内，mapPixelToWorldOriginal返回(-1, -1)
   - 系统将(-1, -1)转换为(0, 0)作为"无效"标记

2. **量化过程**:
   - 物理坐标(0, 0)被量化为原始值(0, 128)
   - X: 0 -> 0 (在0-82范围内，0对应原始值0)
   - Y: 0 -> 128 (在-125到125范围内，0对应原始值128)

3. **反向转换**:
   - 原始值(0, 128)被转换回物理坐标
   - X: 0 -> 0.000 (0/255 × 82 = 0)
   - Y: 128 -> 0.490 (128/255 × 250 - 125 ≈ 0.49)

4. **为什么Y不是0而是0.490**:
   - 这是量化误差！128/255 ≈ 0.502，不是精确的0.5
   - 0.502 × 250 - 125 = 0.49，而不是0

5. **结论**:
   - (0.000, 0.490) 实际上表示"无效区域"
   - 这些像素点在标定过程中无法通过三角形插值计算出有效的物理坐标
   - 通常出现在图像边缘、遮挡区域或标定网格覆盖不到的区域
""")

def main():
    print("Distance Table 像素映射分析工具")
    print("=" * 60)
    
    # 分析特定像素点
    table_path = "CALIB_t12_ceju/build/output/distance_table"
    
    test_pixels = [
        (545, 414),  # 问题像素点
        (545, 444),  # 对比像素点
        (640, 500),  # 中心区域像素点
    ]
    
    for pixel_x, pixel_y in test_pixels:
        analyze_pixel_mapping(table_path, pixel_x, pixel_y)
        print()
    
    # 分析映射逻辑
    analyze_mapping_logic()

if __name__ == "__main__":
    main()
